using HotPreview;

namespace HotPreview.Samples;

// This class would normally be auto-detected as a UI component for preview
// if it inherits from a UI base class, but the PreviewIgnoreAttribute
// prevents it from being auto-detected.
[PreviewIgnore]
public class IgnoredUIComponent
{
    public IgnoredUIComponent()
    {
        // This constructor would normally make this class eligible for auto-generation
        // but PreviewIgnoreAttribute prevents it
    }
}

// This class has both PreviewIgnore and Preview attributes.
// The explicit Preview attribute takes precedence, so this class
// will still have a preview generated.
[PreviewIgnore]
[Preview("Custom Preview Title")]
public class ExplicitPreviewComponent
{
    public ExplicitPreviewComponent()
    {
    }
}

// This class has no attributes, so it would be auto-detected
// if it inherits from a UI base class.
public class AutoDetectedComponent
{
    public AutoDetectedComponent()
    {
    }
}

// This class has an explicit Preview attribute, so it will
// definitely have a preview generated.
[Preview]
public class ExplicitPreviewOnlyComponent
{
    public ExplicitPreviewOnlyComponent()
    {
    }
}
