// Copyright (c) <PERSON><PERSON>. All rights reserved.
// Licensed under the MIT license. See LICENSE file in the project root for full license information.

namespace HotPreview;

/// <summary>
/// A simple calculator class for testing purposes.
/// </summary>
public static class Calculator
{
    /// <summary>
    /// Adds two integers.
    /// </summary>
    /// <param name="a">The first integer.</param>
    /// <param name="b">The second integer.</param>
    /// <returns>The sum of the two integers.</returns>
    public static int Add(int a, int b)
    {
        return a + b;
    }

    /// <summary>
    /// Subtracts the second integer from the first.
    /// </summary>
    /// <param name="a">The first integer.</param>
    /// <param name="b">The second integer.</param>
    /// <returns>The difference of the two integers.</returns>
    public static int Subtract(int a, int b)
    {
        return a - b;
    }
}
