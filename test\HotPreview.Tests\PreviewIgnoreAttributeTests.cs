using System;
using System.Reflection;
using HotPreview;
using Xunit;

namespace HotPreview.Tests;

public class PreviewIgnoreAttributeTests
{
    // This class should NOT be auto-detected (has PreviewIgnoreAttribute)
    [PreviewIgnore]
    public class IgnoredComponent
    {
        public IgnoredComponent() { }
    }

    // This class should be detected (has explicit PreviewAttribute)
    [Preview]
    public class ExplicitPreviewComponent
    {
        public ExplicitPreviewComponent() { }
    }

    // This class should be detected (has both PreviewIgnore and Preview, but Preview takes precedence)
    [PreviewIgnore]
    [Preview]
    public class BothAttributesComponent
    {
        public BothAttributesComponent() { }
    }

    [Fact]
    public void PreviewIgnoreAttribute_ShouldHaveCorrectTypeFullName()
    {
        // Verify that the TypeFullName property works correctly
        string expectedTypeName = "HotPreview.PreviewIgnoreAttribute";
        Assert.Equal(expectedTypeName, PreviewIgnoreAttribute.TypeFullName);
    }

    [Fact]
    public void PreviewIgnoreAttribute_ShouldBeApplicableToClasses()
    {
        // Verify that the attribute can be applied to classes
        Type ignoredComponentType = typeof(IgnoredComponent);
        PreviewIgnoreAttribute? attribute = ignoredComponentType.GetCustomAttribute<PreviewIgnoreAttribute>();

        Assert.NotNull(attribute);
    }

    [Fact]
    public void PreviewIgnoreAttribute_ShouldNotPreventExplicitPreviewAttribute()
    {
        // Verify that when both attributes are present, PreviewAttribute still works
        Type bothAttributesType = typeof(BothAttributesComponent);
        PreviewIgnoreAttribute? ignoreAttribute = bothAttributesType.GetCustomAttribute<PreviewIgnoreAttribute>();
        PreviewAttribute? previewAttribute = bothAttributesType.GetCustomAttribute<PreviewAttribute>();

        Assert.NotNull(ignoreAttribute);
        Assert.NotNull(previewAttribute);
    }

    [Fact]
    public void PreviewIgnoreAttribute_ShouldHaveCorrectAttributeUsage()
    {
        // Verify that the attribute has the correct AttributeUsage
        Type attributeType = typeof(PreviewIgnoreAttribute);
        AttributeUsageAttribute? usage = attributeType.GetCustomAttribute<AttributeUsageAttribute>();

        Assert.NotNull(usage);
        Assert.Equal(AttributeTargets.Class, usage.ValidOn);
    }
}
